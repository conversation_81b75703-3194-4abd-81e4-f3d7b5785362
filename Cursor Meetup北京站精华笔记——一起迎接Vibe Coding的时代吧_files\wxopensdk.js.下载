!function(){"use strict";var t={208:function(t,e,n){var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{u(r.next(t))}catch(t){i(t)}}function s(t){try{u(r.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((r=r.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(e,"__esModule",{value:!0});var i=n(439),a=function(){function t(t,e){this.frameSrc=t,this.methodOutputTarget=e,this.registerSDKFrame()}return t.prototype.registerSDKFrame=function(){var t=this;this.methodOutputTarget.ready=!1;var e=document.createElement("iframe");e.src=this.frameSrc,e.style.display="none",this.frame=e,e.addEventListener("load",(function(){if(!e.contentWindow)return!1;t.channel=new i.CrossSiteWindowMessageChannel(e.contentWindow),t.channel.send("sync_method",void 0).then((function(e){var n,r;e.forEach((function(e){t.methodOutputTarget[e]=t.genTransformMethods(e)})),t.methodOutputTarget.ready=!0,"function"==typeof t.methodOutputTarget.onReady&&(null===(r=(n=t.methodOutputTarget).onReady)||void 0===r||r.call(n))}))})),document.body.appendChild(e)},t.prototype.genTransformMethods=function(t){var e=this;return function(n){return r(e,void 0,void 0,(function(){return o(this,(function(e){switch(e.label){case 0:if(!this.channel)throw new Error("sdk not inited.");return[4,this.channel.send("invoke",{apiName:t,args:n})];case 1:return[2,e.sent()]}}))}))}},t}();e.default=a},576:function(t,e){var n,r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{u(r.next(t))}catch(t){i(t)}}function s(t){try{u(r.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((r=r.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.waitAnimationFrame=e.waitTimeout=e.AsyncQueue=e.AsyncMutex=e.createAsyncRef=e.Waiting=e.AsyncValue=void 0,function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVE=1]="RESOLVE",t[t.REJECT=2]="REJECT"}(n||(n={}));var i=function(){function t(){this.state=n.PENDING,this.resolvePending=null,this.rejectPending=null}return t.prototype.get=function(){return r(this,void 0,void 0,(function(){var t=this;return o(this,(function(e){return this.state===n.RESOLVE?[2,this.value]:this.state===n.REJECT?[2,Promise.reject(this.value)]:(this.pendingRequest||(this.pendingRequest=new Promise((function(e,n){t.resolvePending=e,t.rejectPending=n}))),[2,this.pendingRequest])}))}))},t.prototype.getSync=function(){return this.state===n.PENDING?void 0:this.value},t.prototype.set=function(t,e){if(void 0===e&&(e=!1),this.state!==n.PENDING)throw new Error("forbidden: set value more than once");this.value=t,e?(this.state=n.REJECT,this.rejectPending&&this.rejectPending(this.value)):(this.state=n.RESOLVE,this.resolvePending&&this.resolvePending(this.value)),this.resolvePending=null,this.rejectPending=null,this.pendingRequest=null},t.prototype.isPending=function(){return this.state===n.PENDING},t.prototype.isResolved=function(){return this.state===n.RESOLVE},t.prototype.isRejected=function(){return this.state===n.REJECT},t}();e.AsyncValue=i;var a=function(){function t(){this.value=new i}return t.prototype.waitUntil=function(t){return r(this,void 0,void 0,(function(){var e;return o(this,(function(n){switch(n.label){case 0:return[4,this.value.get()];case 1:return e=n.sent(),"function"!=typeof t?[3,3]:[4,t(e)];case 2:n.sent(),n.label=3;case 3:return[2,e]}}))}))},t.prototype.done=function(t){this.value.set(t)},t}();e.Waiting=a,e.createAsyncRef=function(){var t=new i;return{ref:function(e){null!=e&&e!==t.getSync()&&t.set(e)},get current(){return t.get()},get currentSync(){return t.getSync()}}};var s=function(){function t(){}return t.prototype.lock=function(){var t=this;if(this._lockPromise)throw new Error("mutex is locked!");this._lockPromise=new Promise((function(e){t._resolveHandler=e}))},t.prototype.unlock=function(){this._resolveHandler(),this._lockPromise=void 0},t.prototype.waitUnlock=function(){return this._lockPromise},t.prototype.requestLock=function(){return r(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,this.waitUnlock()];case 1:return t.sent(),[2,this.lock()]}}))}))},t}();e.AsyncMutex=s;var u=function(){function t(){this._queues=[]}return t.prototype.requestTask=function(t){return r(this,void 0,void 0,(function(){var e,n,r,i,a=this;return o(this,(function(o){switch(o.label){case 0:return e=this._queues.length>0?this._queues[this._queues.length-1]:void 0,r=new Promise((function(t){n=t})),i={resolveHandler:n,taskPromise:r},this._queues.push(i),e?[4,null==e?void 0:e.taskPromise]:[3,2];case 1:o.sent(),o.label=2;case 2:return"number"==typeof t&&setTimeout((function(){a._queues.includes(i)&&n()}),t),[2,{done:function(){console.assert(i===a._queues.shift()),n()}}]}}))}))},t}();e.AsyncQueue=u,e.waitTimeout=function(t){return new Promise((function(e){setTimeout(e,t)}))},e.waitAnimationFrame=function(){return new Promise((function(t){requestAnimationFrame(t)}))}},315:function(t,e){var n=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{u(r.next(t))}catch(t){i(t)}}function s(t){try{u(r.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((r=r.apply(t,e||[])).next())}))},r=this&&this.__generator||function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.BaseWindowMessageChannel=void 0;var o=function(){function t(){this._callbacks={},this._id=String(Date.now())}return t.prototype.send=function(t,e){return n(this,arguments,void 0,(function(t,e,n){return void 0===n&&(n={}),r(this,(function(t){throw new Error("unimplented error")}))}))},t.prototype.on=function(t,e){this._callbacks[t]&&console.warn("already has "+t),console.debug("channel received on listener[".concat(t,"]")),this._callbacks[t]=e},t.prototype.emit=function(t,e){if(this._callbacks[t])return this._callbacks[t](e);console.warn("event ".concat(t," haven't listeners!"),this)},t}();e.BaseWindowMessageChannel=o},439:function(t,e,n){var r,o=this&&this.__extends||(r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},r(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),i=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function a(t){try{u(r.next(t))}catch(t){i(t)}}function s(t){try{u(r.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((r=r.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(s){return function(u){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&s[0]?r.return:s[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,s[1])).done)return o;switch(r=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){a.label=s[1];break}if(6===s[0]&&a.label<o[1]){a.label=o[1],o=s;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(s);break}o[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.CrossSiteWindowMessageChannel=void 0;var s=n(576),u=n(315),c=0,l=function(t){function e(e){var n=t.call(this)||this;return n.targetWindow=e,n._callbackPromises={},window.addEventListener("message",(function(t){return i(n,void 0,void 0,(function(){var e,n,r;return a(this,(function(o){switch(o.label){case 0:return t.source!==this.targetWindow?[2]:"_callback"===t.data.name?[3,1]:[3,2];case 1:return null===(r=this._callbackPromises[t.data._id])||void 0===r||r.set(t.data.content),delete this._callbackPromises[t.data._id],[3,4];case 2:return e=this._sendCallback,n=[t.data._id],[4,this.emit(t.data.name,t.data.content)];case 3:e.apply(this,n.concat([o.sent()])),o.label=4;case 4:return[2]}}))}))})),n}return o(e,t),e.prototype._sendCallback=function(t,e){this.targetWindow.postMessage({name:"_callback",content:e,_id:t},"*")},e._processMessage=function(t){return JSON.parse(JSON.stringify(t))},e.prototype.send=function(t,n){var r=c++,o={name:t,content:n,_id:r};o=e._processMessage(o),this.targetWindow.postMessage(o,"*");var i=new s.AsyncValue;return this._callbackPromises[r]=i,i.get()},e}(u.BaseWindowMessageChannel);e.CrossSiteWindowMessageChannel=l},504:function(t,e,n){var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var o=r(n(208)),i={};window.wxopensdk=i,new o.default("https://open.weixin.qq.com/pcopensdk/frame",i)}},e={};!function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}(504)}();